import 'package:flutter/material.dart';

/// 时间范围选项配置类
class TimeRangeOption {
  final String value;
  final String label;
  final String description;
  final IconData icon;

  const TimeRangeOption({
    required this.value,
    required this.label,
    required this.description,
    required this.icon,
  });
}

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Dropdown Test',
      home: TestDropdownPage(),
    );
  }
}

class TestDropdownPage extends StatefulWidget {
  @override
  _TestDropdownPageState createState() => _TestDropdownPageState();
}

class _TestDropdownPageState extends State<TestDropdownPage> {
  // 时间范围选项配置
  final List<TimeRangeOption> _timeRangeOptions = [
    TimeRangeOption(
      value: '最近7天',
      label: '最近7天',
      description: '一周内的数据',
      icon: Icons.calendar_view_week_rounded,
    ),
    TimeRangeOption(
      value: '最近14天',
      label: '最近14天',
      description: '两周内的数据',
      icon: Icons.calendar_view_month_rounded,
    ),
    TimeRangeOption(
      value: '最近30天',
      label: '最近30天',
      description: '一个月内的数据',
      icon: Icons.calendar_month_rounded,
    ),
    TimeRangeOption(
      value: '最近60天',
      label: '最近60天',
      description: '两个月内的数据',
      icon: Icons.date_range_rounded,
    ),
  ];
  String _selectedTimeRange = '最近7天';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Dropdown Test')),
      body: Center(
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: const Color(0xFF1976D2), width: 1.5),
          ),
          child: DropdownMenu<String>(
            initialSelection: _selectedTimeRange,
            width: 140,
            textStyle: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Color(0xFF1976D2),
            ),
            onSelected: (String? newValue) {
              if (newValue != null && newValue != _selectedTimeRange) {
                setState(() {
                  _selectedTimeRange = newValue;
                });
                print('Selected: $newValue');
              }
            },
            dropdownMenuEntries: _timeRangeOptions.map((TimeRangeOption option) {
              final isSelected = option.value == _selectedTimeRange;
              return DropdownMenuEntry<String>(
                value: option.value,
                label: option.label,
                leadingIcon: Icon(
                  isSelected ? Icons.check_circle_rounded : option.icon,
                  size: 18,
                  color: isSelected ? const Color(0xFF1976D2) : Colors.grey[600],
                ),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }
}
